import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.PROD ? '/api' : 'http://localhost:3000/api',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
})

// Request interceptor for adding auth headers if needed
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('varpro-token')
    if (token) {
      config.headers['x-varpro-company-token'] = token
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for handling common errors
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('varpro-token')
      // Could redirect to login page here
    }
    return Promise.reject(error)
  }
)

// API functions for React Query
export const apiService = {
  // Test endpoints
  getApiRoot: async (): Promise<any> => {
    const response = await api.get('/')
    return response.data
  },

  getV1Root: async (): Promise<any> => {
    const response = await api.get('/v1')
    return response.data
  },

  getHello: async (params?: any): Promise<any> => {
    const response = await api.get('/v1/hello', { params })
    return response.data
  },

  // Order endpoints
  getOrderDetails: async (): Promise<any> => {
    const response = await api.get('/v1/order/details')
    return response.data
  },

  // Generic methods
  get: async (url: string, config?: any): Promise<any> => {
    const response = await api.get(url, config)
    return response.data
  },

  post: async (url: string, data?: any, config?: any): Promise<any> => {
    const response = await api.post(url, data, config)
    return response.data
  },

  put: async (url: string, data?: any, config?: any): Promise<any> => {
    const response = await api.put(url, data, config)
    return response.data
  },

  delete: async (url: string, config?: any): Promise<any> => {
    const response = await api.delete(url, config)
    return response.data
  },
}

export default api
