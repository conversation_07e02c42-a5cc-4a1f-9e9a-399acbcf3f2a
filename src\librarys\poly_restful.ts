// import { Customer } from '@app/db/models'
import axios from 'axios'

const VARPRO_RESTFUL_URL = process.env.VARPRO_RESTFUL_URL

const polyTable = async <T>(query: string, columns: string, filter: string) => {
  if (!VARPRO_RESTFUL_URL) {
    throw new Error('VARPRO_RESTFUL_URL is not defined')
  }

  console.log('polyTable', {
    query,
    columns,
    filter,
  })

  const responseData = await axios.post<T>(
    `${VARPRO_RESTFUL_URL}/poly/json_table`,
    {
      query,
      columns,
      filter,
    }
  )

  console.log('responseData', responseData)

  return responseData.data
}

export interface PolyOrderDetails {
  CustomerNumber: string
  OrderStatus: string
  OrderDate: string
  RetailerPONumber: string
  PONumber: string
  RequiredDate: string
  OrderNumber: string
  ItemNumber: string
  StyleNumber: string
  StyleColor: string
  UnitCost: string
  GarmentSize: string
  SKUNumber2: string
  ItemDescription8_: string
  ActualCount: string
  Description: string
  DetailSpec2: string
  DetailSpec3: string
  DetailSpec4: string
  DetailSpec5: string
  AllocateCount: number
  QuantityFinished: number
  PackCount: number
  ShipCount: number
}

export const getPolyActiveOrderDetails = async (customer: any) => {
  const statusFilters = [
    'Committed',
    'New',
    'Pending',
    'Processed',
    'Shipped',
    'Released',
  ]
  const statusFilterString = statusFilters
    .map((status) => {
      return `[OrderStatus] = '${status}'`
    })
    .join(' OR ')

  if (
    !customer.poly_customer_codes ||
    customer.poly_customer_codes.length === 0
  ) {
    throw new Error('Customer does not have Customer Codes')
  }
  let customerFilterString = customer.poly_customer_codes
    .map((code: any) => {
      return `[CustomerNumber] = '${code}'`
    })
    .join(' OR ')

  const columns = [
    'CustomerNumber',
    'OrderStatus',
    'OrderDate',
    'RetailerPONumber',
    'PONumber',
    'RequiredDate',
    'OrderNumber',
    'ItemNumber',
    'StyleNumber',
    'StyleColor',
    'GarmentSize',
    'SKUNumber2',
    'UnitCost',
    'ItemDescription8_',
    'ActualCount',
    'Description',
    'DetailSpec2',
    'DetailSpec3',
    'DetailSpec4',
    'DetailSpec5',
    'AllocateCount',
    'QuantityFinished',
    'PackCount',
    'ShipCount',
  ]
  const columnsString = columns.join(', ')

  const filterString = `(${customerFilterString}) AND (${statusFilterString})`

  const data = await polyTable<PolyOrderDetails[]>(
    'ShowAllOrderDetails',
    columnsString,
    filterString
  )
  return {
    data,
  }
}

/**
 * Get Poly order details filtered by specific PO numbers
 */
export const getPolyOrderDetailsByPONumbers = async (
  customer: any,
  poNumbers: string[]
) => {
  if (
    !customer.poly_customer_codes ||
    customer.poly_customer_codes.length === 0
  ) {
    throw new Error('Customer does not have Customer Codes')
  }

  if (!poNumbers || poNumbers.length === 0) {
    throw new Error('No PO numbers provided')
  }

  const customerFilterString = customer.poly_customer_codes
    .map((code: any) => {
      return `[CustomerNumber] = '${code}'`
    })
    .join(' OR ')

  const poNumberFilterString = poNumbers
    .map((poNumber) => {
      return `[PONumber] = '${poNumber}'`
    })
    .join(' OR ')

  const columns = [
    'CustomerNumber',
    'OrderStatus',
    'OrderDate',
    'RetailerPONumber',
    'PONumber',
    'RequiredDate',
    'OrderNumber',
    'ItemNumber',
    'StyleNumber',
    'StyleColor',
    'GarmentSize',
    'SKUNumber2',
    'UnitCost',
    'ItemDescription8_',
    'ActualCount',
    'Description',
    'DetailSpec2',
    'DetailSpec3',
    'DetailSpec4',
    'DetailSpec5',
    'AllocateCount',
    'QuantityFinished',
    'PackCount',
    'ShipCount',
  ]
  const columnsString = columns.join(', ')

  const filterString = `(${customerFilterString}) AND (${poNumberFilterString})`

  const data = await polyTable<PolyOrderDetails[]>(
    'ShowAllOrderDetails',
    columnsString,
    filterString
  )
  return {
    data,
  }
}
