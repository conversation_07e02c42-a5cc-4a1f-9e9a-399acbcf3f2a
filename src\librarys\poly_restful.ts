// import { Customer } from '@app/db/models'
import axios from 'axios'

const ACTIVE_ORDER_STATUSES = [
  'Committed',
  'New',
  'Pending',
  'Processed',
  'Shipped',
  'Released',
]

const VARPRO_RESTFUL_URL = process.env.VARPRO_RESTFUL_URL

const post = async <T>(url: string, data: any) => {
  if (!VARPRO_RESTFUL_URL) {
    throw new Error('VARPRO_RESTFUL_URL is not defined')
  }

  const useUrl = `${VARPRO_RESTFUL_URL}${url}`

  const responseData = await axios.post<T>(useUrl, data)
  return responseData
}

const polyTable = async <T>(
  query: string,
  columns: string[],
  filter: string
) => {
  const columnString = columns.join(', ')
  return post<T>('/poly/json_table', {
    query,
    columnString,
    filter,
  })
}

export interface PolyOrderDetails {
  CustomerNumber: string
  OrderStatus: string
  OrderDate: string
  RetailerPONumber: string
  PONumber: string
  RequiredDate: string
  OrderNumber: string
  ItemNumber: string
  StyleNumber: string
  StyleColor: string
  UnitCost: string
  GarmentSize: string
  SKUNumber2: string
  ItemDescription8_: string
  ActualCount: string
  Description: string
  DetailSpec2: string
  DetailSpec3: string
  DetailSpec4: string
  DetailSpec5: string
  AllocateCount: number
  QuantityFinished: number
  PackCount: number
  ShipCount: number
}

const polyOrderDetails = (filter: string) => {
  return polyTable<PolyOrderDetails[]>(
    'ShowAllOrderDetails',
    [
      'CustomerNumber',
      'OrderStatus',
      'OrderDate',
      'RetailerPONumber',
      'PONumber',
      'RequiredDate',
      'OrderNumber',
      'ItemNumber',
      'StyleNumber',
      'StyleColor',
      'GarmentSize',
      'SKUNumber2',
      'UnitCost',
      'ItemDescription8_',
      'ActualCount',
      'Description',
      'DetailSpec2',
      'DetailSpec3',
      'DetailSpec4',
      'DetailSpec5',
      'AllocateCount',
      'QuantityFinished',
      'PackCount',
      'ShipCount',
    ],
    filter
  )
}

export const getPolyActiveOrderDetails = async (customer: any) => {
  const statusFilterString = ACTIVE_ORDER_STATUSES.map((status) => {
    return `[OrderStatus] = '${status}'`
  }).join(' OR ')

  const filterString = `(${statusFilterString})`
  const data = await polyOrderDetails(filterString)

  return {
    data: data.data,
  }
}

interface MoPartNumbers {
  ManufactureNumber: string
  PartNumber: string
  Description: string
  CategoryName: string
  SubcategoryName: string
  DatabaseUnits: string
  QuantityRequired: string
  QuantityWithdrawn: string
  QuantityOnHand: string
}

const getMoPartNumbers = async (filter: string) => {
  return polyTable<MoPartNumbers[]>(
    'ShowAllRawAllocations',
    [
      'ManufactureNumber',
      'PartNumber',
      'Description',
      'CategoryName',
      'SubcategoryName',
      'DatabaseUnits',
      'QuantityRequired',
      'QuantityWithdrawn',
      'QuantityOnHand',
    ],
    filter
  )
}

export const getMoPartNumbersByMo = async (
  num: string,
  categories?: ('Care Labels' | 'Trim')[]
) => {
  const categoryFilter = categories
    ?.map((category) => {
      return `[CategoryName] = '${category}'`
    })
    .join(' OR ')

  const data = await getMoPartNumbers(
    `[ManufactureNumber] = '${num}'${(categories?.length || 0) > 0 ? ` AND (${categoryFilter})` : ''}`
  )

  return data.data
}
