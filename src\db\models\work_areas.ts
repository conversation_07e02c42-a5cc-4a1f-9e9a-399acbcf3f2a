import { ColumnType, Generated } from 'kysely'

// Work Areas table
export interface WorkAreasTable {
  work_area_id: Generated<number>
  work_factory_id: number
  work_type_id: number
  area_name: string
  default_work_voucher_type_id: number
  sort: number
  disabled_date: ColumnType<Date, string | undefined, string | undefined> | null
  auto_ignore_next_area: boolean
  poly_task_name: string | null
  supervisor_id: number | null
  work_status_id: number
  after_scan_work_area_ticket_status_id: number | null
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
  removed_at: ColumnType<Date, string | undefined, string | undefined> | null
  work_building_id: number | null
}
