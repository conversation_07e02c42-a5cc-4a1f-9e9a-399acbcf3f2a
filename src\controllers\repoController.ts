import { Request, Response } from 'express'
import { StatusCodes, ReasonPhrases } from 'http-status-codes'
import { repoService } from '../services/repoService'
import {
  createWorkRepoSchema,
  updateWorkRepoSchema,
  createWorkRepoMaterialSchema,
  updateWorkRepoMaterialSchema,
  createWorkRepoPieceSchema,
  updateWorkRepoPieceSchema,
  createWorkRepoStatusSchema,
  updateWorkRepoStatusSchema,
  createWorkRepoNoteSchema,
  updateWorkRepoNoteSchema,
  repoQuerySchema,
  materialQuerySchema,
  pieceQuerySchema,
  statusQuerySchema,
  noteQuerySchema,
  idParamSchema,
} from '../zod/repoSchemas'

// ===== WORK REPOS =====

export const getAllRepos = async (req: Request, res: Response) => {
  try {
    const query = repoQuerySchema.parse(req.query)

    if (query.search) {
      const repos = await repoService.searchRepos(query.search)
      res.status(StatusCodes.OK).json({
        success: true,
        data: repos,
        count: repos.length,
      })
    } else if (query.work_repo_status_id) {
      const repos = await repoService.getReposByStatusId(
        query.work_repo_status_id
      )
      res.status(StatusCodes.OK).json({
        success: true,
        data: repos,
        count: repos.length,
      })
    } else if (query.creator_email) {
      const repos = await repoService.getReposByCreatorEmail(
        query.creator_email
      )
      res.status(StatusCodes.OK).json({
        success: true,
        data: repos,
        count: repos.length,
      })
    } else if (query.repo_type) {
      const repos = await repoService.getReposByType(query.repo_type)
      res.status(StatusCodes.OK).json({
        success: true,
        data: repos,
        count: repos.length,
      })
    } else if (query.mo_id) {
      const repos = await repoService.getReposByMoId(query.mo_id)
      res.status(StatusCodes.OK).json({
        success: true,
        data: repos,
        count: repos.length,
      })
    } else {
      const result = await repoService.getAllRepos()
      res.status(StatusCodes.OK).json({
        success: true,
        data: result.data,
        count: result.total,
        hasMore: result.hasMore,
      })
    }
  } catch (error) {
    console.error('Error getting repos:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const getRepoById = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const repo = await repoService.getRepoById(id)

    if (!repo) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Repository not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: repo,
    })
  } catch (error) {
    console.error('Error getting repo:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const getRepoWithAllData = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const repoData = await repoService.getRepoWithAllData(id)

    if (!repoData.repo) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Repository not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: repoData,
    })
  } catch (error) {
    console.error('Error getting repo with all data:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const getRepoSummary = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const summary = await repoService.getRepoSummary(id)

    if (!summary.repo) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Repository not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: summary,
    })
  } catch (error) {
    console.error('Error getting repo summary:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const createRepo = async (req: Request, res: Response) => {
  try {
    const repoData = createWorkRepoSchema.parse(req.body)
    const newRepo = await repoService.createRepo(repoData)

    res.status(StatusCodes.CREATED).json({
      success: true,
      data: newRepo,
      message: 'Repository created successfully',
    })
  } catch (error) {
    console.error('Error creating repo:', error)
    res.status(StatusCodes.BAD_REQUEST).json({
      success: false,
      message: 'Failed to create repository',
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const updateRepo = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const updates = updateWorkRepoSchema.parse(req.body)

    const updatedRepo = await repoService.updateRepo(id, updates)

    if (!updatedRepo) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Repository not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: updatedRepo,
      message: 'Repository updated successfully',
    })
  } catch (error) {
    console.error('Error updating repo:', error)
    res.status(StatusCodes.BAD_REQUEST).json({
      success: false,
      message: 'Failed to update repository',
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const deleteRepo = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const deleted = await repoService.deleteRepo(id)

    if (!deleted) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Repository not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Repository deleted successfully',
    })
  } catch (error) {
    console.error('Error deleting repo:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// ===== WORK REPO MATERIALS =====

export const getMaterialsByRepoId = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const materials = await repoService.getMaterialsByRepoId(id)

    res.status(StatusCodes.OK).json({
      success: true,
      data: materials,
      count: materials.length,
    })
  } catch (error) {
    console.error('Error getting materials:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const createMaterial = async (req: Request, res: Response) => {
  try {
    const materialData = createWorkRepoMaterialSchema.parse(req.body)
    const newMaterial = await repoService.createMaterial(materialData)

    res.status(StatusCodes.CREATED).json({
      success: true,
      data: newMaterial,
      message: 'Material created successfully',
    })
  } catch (error) {
    console.error('Error creating material:', error)
    res.status(StatusCodes.BAD_REQUEST).json({
      success: false,
      message: 'Failed to create material',
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const updateMaterial = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const updates = updateWorkRepoMaterialSchema.parse(req.body)

    const updatedMaterial = await repoService.updateMaterial(id, updates)

    if (!updatedMaterial) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Material not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: updatedMaterial,
      message: 'Material updated successfully',
    })
  } catch (error) {
    console.error('Error updating material:', error)
    res.status(StatusCodes.BAD_REQUEST).json({
      success: false,
      message: 'Failed to update material',
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const deleteMaterial = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const deleted = await repoService.deleteMaterial(id)

    if (!deleted) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Material not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Material deleted successfully',
    })
  } catch (error) {
    console.error('Error deleting material:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// ===== WORK REPO PIECES =====

export const getPiecesByRepoId = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const pieces = await repoService.getPiecesByRepoId(id)

    res.status(StatusCodes.OK).json({
      success: true,
      data: pieces,
      count: pieces.length,
    })
  } catch (error) {
    console.error('Error getting pieces:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const createPiece = async (req: Request, res: Response) => {
  try {
    const pieceData = createWorkRepoPieceSchema.parse(req.body)
    const newPiece = await repoService.createPiece(pieceData)

    res.status(StatusCodes.CREATED).json({
      success: true,
      data: newPiece,
      message: 'Piece created successfully',
    })
  } catch (error) {
    console.error('Error creating piece:', error)
    res.status(StatusCodes.BAD_REQUEST).json({
      success: false,
      message: 'Failed to create piece',
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const updatePiece = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const updates = updateWorkRepoPieceSchema.parse(req.body)

    const updatedPiece = await repoService.updatePiece(id, updates)

    if (!updatedPiece) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Piece not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: updatedPiece,
      message: 'Piece updated successfully',
    })
  } catch (error) {
    console.error('Error updating piece:', error)
    res.status(StatusCodes.BAD_REQUEST).json({
      success: false,
      message: 'Failed to update piece',
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const deletePiece = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const deleted = await repoService.deletePiece(id)

    if (!deleted) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Piece not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Piece deleted successfully',
    })
  } catch (error) {
    console.error('Error deleting piece:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// ===== WORK REPO STATUSES (Lookup Table) =====

export const getAllStatuses = async (req: Request, res: Response) => {
  try {
    const statuses = await repoService.getAllStatuses()

    res.status(StatusCodes.OK).json({
      success: true,
      data: statuses,
      count: statuses.length,
    })
  } catch (error) {
    console.error('Error getting statuses:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const getStatusById = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const status = await repoService.getStatusById(id)

    if (!status) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Status not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: status,
    })
  } catch (error) {
    console.error('Error getting status:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const createStatus = async (req: Request, res: Response) => {
  try {
    const statusData = createWorkRepoStatusSchema.parse(req.body)
    const newStatus = await repoService.createStatus(statusData)

    res.status(StatusCodes.CREATED).json({
      success: true,
      data: newStatus,
      message: 'Status created successfully',
    })
  } catch (error) {
    console.error('Error creating status:', error)
    res.status(StatusCodes.BAD_REQUEST).json({
      success: false,
      message: 'Failed to create status',
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// ===== WORK REPO NOTES =====

export const getNotesByRepoId = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const notes = await repoService.getNotesByRepoId(id)

    res.status(StatusCodes.OK).json({
      success: true,
      data: notes,
      count: notes.length,
    })
  } catch (error) {
    console.error('Error getting notes:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const createNote = async (req: Request, res: Response) => {
  try {
    const noteData = createWorkRepoNoteSchema.parse(req.body)
    const newNote = await repoService.createNote(noteData)

    res.status(StatusCodes.CREATED).json({
      success: true,
      data: newNote,
      message: 'Note created successfully',
    })
  } catch (error) {
    console.error('Error creating note:', error)
    res.status(StatusCodes.BAD_REQUEST).json({
      success: false,
      message: 'Failed to create note',
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const updateNote = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const updates = updateWorkRepoNoteSchema.parse(req.body)

    const updatedNote = await repoService.updateNote(id, updates)

    if (!updatedNote) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Note not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: updatedNote,
      message: 'Note updated successfully',
    })
  } catch (error) {
    console.error('Error updating note:', error)
    res.status(StatusCodes.BAD_REQUEST).json({
      success: false,
      message: 'Failed to update note',
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const deleteNote = async (req: Request, res: Response) => {
  try {
    const { id } = idParamSchema.parse(req.params)
    const deleted = await repoService.deleteNote(id)

    if (!deleted) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Note not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Note deleted successfully',
    })
  } catch (error) {
    console.error('Error deleting note:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
