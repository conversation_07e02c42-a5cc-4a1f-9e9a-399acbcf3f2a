import { db } from '../db/database'
import { WorkArea } from '../db/models'

export class WorkAreaService {
  
  async getAllWorkAreas(): Promise<WorkArea[]> {
    return await db
      .selectFrom('work_areas')
      .selectAll()
      .where('removed_at', 'is', null)
      .orderBy('area_name', 'asc')
      .execute()
  }

  async getWorkAreaById(workAreaId: number): Promise<WorkArea | undefined> {
    return await db
      .selectFrom('work_areas')
      .selectAll()
      .where('work_area_id', '=', workAreaId)
      .where('removed_at', 'is', null)
      .executeTakeFirst()
  }

  async searchWorkAreas(searchTerm: string, limit: number = 10): Promise<WorkArea[]> {
    return await db
      .selectFrom('work_areas')
      .selectAll()
      .where('removed_at', 'is', null)
      .where((eb) =>
        eb.or([
          eb('area_name', 'like', `%${searchTerm}%`),
          eb('work_area_id', '=', isNaN(parseInt(searchTerm)) ? -1 : parseInt(searchTerm))
        ])
      )
      .orderBy('area_name', 'asc')
      .limit(limit)
      .execute()
  }

  async getActiveWorkAreas(): Promise<WorkArea[]> {
    return await db
      .selectFrom('work_areas')
      .selectAll()
      .where('removed_at', 'is', null)
      .where('disabled_date', 'is', null)
      .orderBy('sort', 'asc')
      .orderBy('area_name', 'asc')
      .execute()
  }

  async getWorkAreasByIds(workAreaIds: number[]): Promise<WorkArea[]> {
    if (workAreaIds.length === 0) return []
    
    return await db
      .selectFrom('work_areas')
      .selectAll()
      .where('work_area_id', 'in', workAreaIds)
      .where('removed_at', 'is', null)
      .orderBy('area_name', 'asc')
      .execute()
  }
}

// Export a singleton instance
export const workAreaService = new WorkAreaService()
