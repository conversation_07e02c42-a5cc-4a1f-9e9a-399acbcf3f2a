# Varpro Customer API

A full-stack application with Express.js API server and React client for managing customer orders and API interactions.

## Project Structure

- **Server**: Express.js API with TypeScript, Kysely ORM, and PostgreSQL
- **Client**: React application with Vite, React Router, and Axios

## Quick Start

### Development (Both Server and Client)

```bash
# Install server dependencies
npm install

# Install client dependencies
npm run install:client

# Run both server and client in development mode
npm run dev:full
```

### Server Only

```bash
npm run dev
```

### Client Only

```bash
npm run dev:client
```

## Available Scripts

- `npm run dev` - Start server in development mode
- `npm run dev:client` - Start client development server
- `npm run dev:full` - Start both server and client concurrently
- `npm run build` - Build both server and client for production
- `npm run build:server` - Build server only
- `npm run build:client` - Build client only
- `npm run install:client` - Install client dependencies

## URLs

- **Server**: http://localhost:3000
- **Client**: http://localhost:5173
- **API Documentation**: http://localhost:3000/docs (if available)

## Client Features

The React client includes:

- **Home Page**: Welcome and feature overview
- **Orders Page**: Order management with API authentication
- **API Test Page**: Test different API endpoints
- **Responsive Design**: Works on desktop and mobile
- **TypeScript**: Full type safety
- **React Router**: Client-side routing
- **React Query**: Advanced state management with caching, background refetching, and optimistic updates
- **React Query DevTools**: Visual debugging and monitoring
- **Axios Integration**: HTTP client used internally by React Query

## DB

### Migration

To create a migration
`npx kysely migrate:make table-some_table_name`
`npx kysely migrate:make column-some_column-some_table_name`

To run a single migration
`npx kysely migrate:up`

To run all unrun migrations
`npx kysely migrate:latest`

To undo the last migration run
`npx kysely migrate:down`
