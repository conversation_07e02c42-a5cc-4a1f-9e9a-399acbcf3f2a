{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.0", "typescript": "~5.8.3", "vite": "^6.3.5"}, "dependencies": {"@tanstack/react-query": "^5.76.2", "@tanstack/react-query-devtools": "^5.76.2", "axios": "^1.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0"}}