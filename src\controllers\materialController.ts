import { Request, Response } from 'express'
import { StatusCodes, ReasonPhrases } from 'http-status-codes'
import { materialService } from '../services/materialService'
import { z } from 'zod'

// Schema for material search query parameters
const materialSearchSchema = z.object({
  search: z.string().optional(),
  limit: z.coerce.number().int().positive().max(50).default(10),
})

export const searchMaterials = async (req: Request, res: Response) => {
  try {
    const { search, limit } = materialSearchSchema.parse(req.query)
    
    let materials
    if (search && search.trim()) {
      materials = await materialService.searchMaterials(search.trim(), limit)
    } else {
      materials = await materialService.getPopularMaterials(limit)
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: materials,
      count: materials.length,
    })
  } catch (error) {
    console.error('Error searching materials:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const getMaterialByPartNumber = async (req: Request, res: Response) => {
  try {
    const { partNumber } = req.params
    
    if (!partNumber) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Part number is required',
      })
      return
    }

    const material = await materialService.getMaterialByPartNumber(partNumber)
    
    if (!material) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Material not found',
      })
      return
    }

    res.status(StatusCodes.OK).json({
      success: true,
      data: material,
    })
  } catch (error) {
    console.error('Error getting material:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: ReasonPhrases.INTERNAL_SERVER_ERROR,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
