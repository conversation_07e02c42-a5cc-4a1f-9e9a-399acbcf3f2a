import { db } from '../db/database'

export interface MaterialSearchResult {
  part_number: string
  unit_of_measure: string | null
  usage_count: number
}

export class MaterialService {
  
  async searchMaterials(searchTerm: string, limit: number = 10): Promise<MaterialSearchResult[]> {
    // Search for unique part numbers from existing work_repo_materials
    const results = await db
      .selectFrom('work_repo_materials')
      .select([
        'part_number',
        'unit_of_measure',
        (eb) => eb.fn.count('part_number').as('usage_count')
      ])
      .where('part_number', 'is not', null)
      .where('part_number', 'like', `%${searchTerm}%`)
      .groupBy(['part_number', 'unit_of_measure'])
      .orderBy('usage_count', 'desc')
      .orderBy('part_number', 'asc')
      .limit(limit)
      .execute()

    return results.map(result => ({
      part_number: result.part_number || '',
      unit_of_measure: result.unit_of_measure,
      usage_count: Number(result.usage_count)
    }))
  }

  async getPopularMaterials(limit: number = 10): Promise<MaterialSearchResult[]> {
    // Get most commonly used materials
    const results = await db
      .selectFrom('work_repo_materials')
      .select([
        'part_number',
        'unit_of_measure',
        (eb) => eb.fn.count('part_number').as('usage_count')
      ])
      .where('part_number', 'is not', null)
      .groupBy(['part_number', 'unit_of_measure'])
      .orderBy('usage_count', 'desc')
      .orderBy('part_number', 'asc')
      .limit(limit)
      .execute()

    return results.map(result => ({
      part_number: result.part_number || '',
      unit_of_measure: result.unit_of_measure,
      usage_count: Number(result.usage_count)
    }))
  }

  async getMaterialByPartNumber(partNumber: string): Promise<MaterialSearchResult | undefined> {
    const result = await db
      .selectFrom('work_repo_materials')
      .select([
        'part_number',
        'unit_of_measure',
        (eb) => eb.fn.count('part_number').as('usage_count')
      ])
      .where('part_number', '=', partNumber)
      .groupBy(['part_number', 'unit_of_measure'])
      .executeTakeFirst()

    if (!result) return undefined

    return {
      part_number: result.part_number || '',
      unit_of_measure: result.unit_of_measure,
      usage_count: Number(result.usage_count)
    }
  }
}

// Export a singleton instance
export const materialService = new MaterialService()
