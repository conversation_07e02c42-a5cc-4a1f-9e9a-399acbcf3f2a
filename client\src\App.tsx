import { Routes, Route, Link } from 'react-router-dom'
import Home from './pages/Home'
import Orders from './pages/Orders'
import ApiTest from './pages/ApiTest'
import './App.css'

function App() {
  return (
    <div className="app">
      <nav className="navbar">
        <div className="nav-container">
          <h1 className="nav-title">Varpro Customer Portal</h1>
          <ul className="nav-menu">
            <li className="nav-item">
              <Link to="/" className="nav-link">Home</Link>
            </li>
            <li className="nav-item">
              <Link to="/orders" className="nav-link">Orders</Link>
            </li>
            <li className="nav-item">
              <Link to="/api-test" className="nav-link">API Test</Link>
            </li>
          </ul>
        </div>
      </nav>

      <main className="main-content">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/orders" element={<Orders />} />
          <Route path="/api-test" element={<ApiTest />} />
        </Routes>
      </main>
    </div>
  )
}

export default App
