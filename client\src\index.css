/* Global Styles */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: #213547;
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  --primary-color: #646cff;
  --primary-hover: #535bf2;
  --secondary-color: #f9f9f9;
  --text-color: #213547;
  --border-color: #e0e0e0;
  --error-color: #ff4444;
  --success-color: #44ff44;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

a {
  font-weight: 500;
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  color: var(--primary-hover);
}

/* App Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Navigation */
.navbar {
  background-color: white;
  border-bottom: 1px solid var(--border-color);
  padding: 0 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.nav-title {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-item {
  margin: 0;
}

.nav-link {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: var(--secondary-color);
}

/* Main Content */
.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
}

/* Page Styles */
.page {
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 2.5rem;
}

.page-description {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* Buttons */
.btn {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.25s;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: #e9e9e9;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.2);
}

/* Grid Layouts */
.features-grid,
.endpoints-grid,
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

/* Cards */
.feature-card,
.endpoint-card,
.order-card {
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-card h3,
.endpoint-card h3,
.order-card h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-color);
}

/* Messages */
.error-message {
  background-color: #ffebee;
  border: 1px solid var(--error-color);
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
  color: #c62828;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

/* Response Display */
.response-section {
  margin-top: 2rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.response-data pre {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.9rem;
}

/* Specific Sections */
.api-key-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.api-key-form {
  display: flex;
  gap: 1rem;
  align-items: end;
}

.getting-started {
  margin-top: 3rem;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.getting-started h2 {
  margin-top: 0;
  color: var(--primary-color);
}

.getting-started ol {
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.no-orders {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.api-info {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

/* React Query specific styles */
.query-status {
  margin-top: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
}

.query-status.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.query-status.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.auth-warning {
  color: #856404;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  margin: 0.5rem 0;
}

.error-response {
  background-color: #f8f9fa;
  border: 1px solid var(--error-color);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.success-response {
  background-color: #f8f9fa;
  border: 1px solid #28a745;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    gap: 1rem;
  }

  .main-content {
    padding: 1rem;
  }

  .page {
    padding: 1rem;
  }

  .features-grid,
  .endpoints-grid,
  .orders-grid {
    grid-template-columns: 1fr;
  }

  .api-key-form {
    flex-direction: column;
    align-items: stretch;
  }
}
