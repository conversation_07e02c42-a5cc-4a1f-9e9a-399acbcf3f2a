import 'dotenv/config'
import express, { Express, Request, Response } from 'express'
import { apiRouter } from './routes/api'
import { getHomePage } from './controllers/homeController'
import {
  getCreateRepoPage,
  getActiveReposPage,
  getPrepareReposPage,
} from './controllers/pagesController'
import path from 'path'
import cors from 'cors'

export const server = () => {
  const app: Express = express()
  const port = process.env.PORT || 3000

  // Enable CORS for development
  app.use(
    cors({
      origin:
        process.env.NODE_ENV === 'production'
          ? false
          : ['http://localhost:5173', 'http://localhost:3000'],
      credentials: true,
    })
  )

  // Serve static files from client build in production
  if (process.env.NODE_ENV === 'production') {
    app.use(express.static(path.join(__dirname, '../client/dist')))
  }

  // Home page route - show repository statistics
  app.get('/', getHomePage)

  // Additional page routes
  app.get('/create-repo', getCreateRepoPage)
  app.get('/create-repo/form', getCreateRepoPage) // This will handle the form with mo_id parameter
  app.get('/active-repos', getActiveReposPage)
  app.get('/prepare-repos', getPrepareReposPage)

  app.use('/api', apiRouter)

  // Serve React app for all non-API routes in production
  if (process.env.NODE_ENV === 'production') {
    app.get('*', (_req: Request, res: Response) => {
      res.sendFile(path.join(__dirname, '../client/dist/index.html'))
    })
  }

  app.listen(port, () => {
    console.log(`Server is running at http://localhost:${port}`)
  })
}
server()
