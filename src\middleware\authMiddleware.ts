import { Request, Response, NextFunction } from 'express'
import { ReasonPhrases, StatusCodes } from 'http-status-codes'
import { db } from '../db/database'
// import { Customer } from '../db/models' // Commented out for now

export const apiAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Simple auth check for now - just check if API key is present
  const apiKey = req.headers['x-varpro-company-token'] as string | undefined
  if (!apiKey) {
    res
      .status(StatusCodes.UNAUTHORIZED)
      .json({ error: ReasonPhrases.UNAUTHORIZED })
    return
  }

  // For now, just pass through - database auth will be implemented later
  res.locals.customer = { id: 1, name: 'Test Customer' }
  res.locals.customer_app_id = 1

  next()
  return
}
