{"name": "varpro-customer-api", "version": "0.1.9", "main": "index.js", "scripts": {"build": "npm run clean && tsc && copyfiles public/**/* dist && tsc-alias", "clean": "rimraf ./dist", "test": "echo \"Error: no test specified\" && exit 1", "dev": "npx tsc --noEmit && npx tsx --watch src/index.ts", "dev:full": "npm run dev", "db:inspect": "npx tsx src/utils/dbInspector.ts", "db:query": "npx tsx src/utils/dbQuery.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@eslint/js": "^9.23.0", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/node": "^22.14.0", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "concurrently": "^9.1.2", "copyfiles": "^2.4.1", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "globals": "^16.0.0", "kysely-ctl": "^0.12.2", "prettier": "^3.5.3", "rimraf": "^6.0.1", "tsc-alias": "^1.8.16", "tsx": "^4.19.3", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0"}, "dependencies": {"ajv": "^8.17.1", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "http-status-codes": "^2.3.0", "kysely": "^0.27.6", "mysql2": "^3.14.0", "uuid": "^11.1.0", "yaml": "^2.8.0", "zod": "^3.24.2", "zod-openapi": "^4.2.4"}}