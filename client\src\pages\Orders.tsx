import React, { useState } from 'react'
import { useOrderDetails, useApiKey } from '../hooks/useApi'

const Orders: React.FC = () => {
  const { setApiKey, getApiKey, hasApiKey } = useApiKey()
  const [apiKeyInput, setApiKeyInput] = useState(getApiKey() || '')

  // Use React Query to fetch orders
  const {
    data: orders = [],
    isLoading,
    error,
    refetch,
  } = useOrderDetails(hasApiKey)

  const handleApiKeySubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (apiKeyInput.trim()) {
      setApiKey(apiKeyInput.trim())
      // React Query will automatically refetch when the API key changes
    }
  }

  const handleRefresh = () => {
    refetch()
  }

  return (
    <div className="page">
      <div className="page-header">
        <h1>Order Management</h1>
        <p className="page-description">
          View and manage your orders through the Varpro API.
        </p>
      </div>

      <div className="api-key-section">
        <form onSubmit={handleApiKeySubmit} className="api-key-form">
          <div className="form-group">
            <label htmlFor="apiKey">API Key:</label>
            <input
              type="password"
              id="apiKey"
              value={apiKeyInput}
              onChange={(e) => setApiKeyInput(e.target.value)}
              placeholder="Enter your Varpro API key"
              className="form-input"
            />
          </div>
          <button type="submit" className="btn btn-primary">
            {hasApiKey ? 'Update API Key' : 'Set API Key'}
          </button>
          {hasApiKey && (
            <button type="button" onClick={handleRefresh} className="btn btn-secondary" style={{ marginLeft: '1rem' }}>
              Refresh Orders
            </button>
          )}
        </form>
      </div>

      {error && (
        <div className="error-message">
          <p>Error: {error.message || String(error)}</p>
        </div>
      )}

      {isLoading && (
        <div className="loading">
          <p>Loading orders...</p>
        </div>
      )}

      {orders.length > 0 && (
        <div className="orders-section">
          <h2>Your Orders</h2>
          <div className="orders-grid">
            {orders.map((order: any, index: number) => (
              <div key={order.id || index} className="order-card">
                <h3>Order #{order.id || index + 1}</h3>
                <p><strong>Customer:</strong> {order.customerNumber}</p>
                <p><strong>Status:</strong> {order.orderStatus}</p>
                <p><strong>Date:</strong> {order.orderDate}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {!isLoading && !error && orders.length === 0 && hasApiKey && (
        <div className="no-orders">
          <p>No orders found. This could mean:</p>
          <ul>
            <li>You don't have any orders in the system</li>
            <li>Your API key doesn't have access to order data</li>
            <li>The order endpoint is not yet implemented</li>
          </ul>
        </div>
      )}
    </div>
  )
}

export default Orders
