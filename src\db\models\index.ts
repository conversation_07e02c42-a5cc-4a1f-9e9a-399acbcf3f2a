import { Insertable, Selectable, Updateable } from 'kysely'
import { ApiRequestTable } from './api_requests'
import {
  WorkRepoTable,
  WorkRepoMaterialTable,
  WorkRepoPieceTable,
  WorkRepoStatusTable,
  WorkRepoNoteTable,
} from './work_repos'
import { MoNumbersTable } from './mo_numbers'
import { MoScansTable } from './mo_scans'
import { WorkAreasTable } from './work_areas'

export interface Database {
  api_requests: ApiRequestTable
  work_repos: WorkRepoTable
  work_repo_materials: WorkRepoMaterialTable
  work_repo_pieces: WorkRepoPieceTable
  work_repo_statuses: WorkRepoStatusTable
  work_repo_notes: WorkRepoNoteTable
  mo_numbers: MoNumbersTable
  mo_scans: MoScansTable
  work_areas: WorkAreasTable
}

// You should not use the table schema interfaces directly. Instead, you should
// use the `Selectable`, `Insertable` and `Updateable` wrappers. These wrappers
// make sure that the correct types are used in each operation.
//
// Most of the time you should trust the type inference and not use explicit
// types at all. These types can be useful when typing function arguments.

export type ApiRequest = Selectable<ApiRequestTable>
export type NewApiRequest = Insertable<ApiRequestTable>
export type ApiRequestUpdate = Updateable<ApiRequestTable>

// Work Repo Types
export type WorkRepo = Selectable<WorkRepoTable>
export type NewWorkRepo = Insertable<WorkRepoTable>
export type WorkRepoUpdate = Updateable<WorkRepoTable>

export type WorkRepoMaterial = Selectable<WorkRepoMaterialTable>
export type NewWorkRepoMaterial = Insertable<WorkRepoMaterialTable>
export type WorkRepoMaterialUpdate = Updateable<WorkRepoMaterialTable>

export type WorkRepoPiece = Selectable<WorkRepoPieceTable>
export type NewWorkRepoPiece = Insertable<WorkRepoPieceTable>
export type WorkRepoPieceUpdate = Updateable<WorkRepoPieceTable>

export type WorkRepoStatus = Selectable<WorkRepoStatusTable>
export type NewWorkRepoStatus = Insertable<WorkRepoStatusTable>
export type WorkRepoStatusUpdate = Updateable<WorkRepoStatusTable>

export type WorkRepoNote = Selectable<WorkRepoNoteTable>
export type NewWorkRepoNote = Insertable<WorkRepoNoteTable>
export type WorkRepoNoteUpdate = Updateable<WorkRepoNoteTable>

// MO Numbers Types
export type MoNumber = Selectable<MoNumbersTable>
export type NewMoNumber = Insertable<MoNumbersTable>
export type MoNumberUpdate = Updateable<MoNumbersTable>

// MO Scans Types
export type MoScan = Selectable<MoScansTable>
export type NewMoScan = Insertable<MoScansTable>
export type MoScanUpdate = Updateable<MoScansTable>

// Work Areas Types
export type WorkArea = Selectable<WorkAreasTable>
export type NewWorkArea = Insertable<WorkAreasTable>
export type WorkAreaUpdate = Updateable<WorkAreasTable>
