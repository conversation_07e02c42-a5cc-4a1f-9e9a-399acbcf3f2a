import { ColumnType, Generated } from 'kysely'

// Manufacturing Orders table
export interface MoNumbersTable {
  mo_id: Generated<number>
  parent_mo_id: number | null
  is_child: boolean
  poly_manufacture_id: number
  customer: string
  customers: string | null
  mo_order: string | null
  mo_order_item_num: string | null
  mo_order_parent_mo: string | null
  mo_status: string | null
  num: string
  required_date: ColumnType<Date, string | undefined, string | undefined> | null
  scheduled_xfac_date: ColumnType<Date, string | undefined, string | undefined> | null
  plan_xfac_date: ColumnType<Date, string | undefined, string | undefined> | null
  create_date: ColumnType<Date, string | undefined, string | undefined> | null
  finish_date: ColumnType<Date, string | undefined, string | undefined> | null
  style: string
  internal_style_id: number | null
  style_category: string | null
  product_category: string | null
  quantity: number
  comment: string | null
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined> | null
  po_number: string
  running_task: string
  mo_barcode: string
  order_date: ColumnType<Date, string | undefined, string | undefined> | null
  order_required_date: ColumnType<Date, string | undefined, string | undefined> | null
  style_id: number | null
  order_status: string | null
  material_date: ColumnType<Date, string | undefined, string | undefined> | null
  company_code: number | null
  ItemDescription8: string | null
  retail_po: string | null
  sched_start: ColumnType<Date, string | undefined, string | undefined> | null
  material_comments: string | null
  withdraw_comments: string | null
  order_ship_date: ColumnType<Date, string | undefined, string | undefined> | null
  order_numbers: string | null
  status_change_date: ColumnType<Date, string | undefined, string | undefined> | null
  production_status: string | null
  retailer_po_numbers: string | null
  po_numbers: string | null
  ItemDescription14: string | null
  voucher_ready: ColumnType<Date, string | undefined, string | undefined> | null
  order_type: string | null
  original_required_date: ColumnType<Date, string | undefined, string | undefined> | null
  order_type_name: string | null
  order_type_2: string | null
  order_type_3: string | null
  cut_date: ColumnType<Date, string | undefined, string | undefined> | null
  missing_on_sync: boolean
  plan_sew_line: string | null
  plan_sew_manager: string | null
  plan_sew_location: string | null
  plan_notes: string | null
  plan_new_xfac_text: string | null
  plan_proceso: string | null
  plan_comment: string | null
  plan_tipo: string | null
  plan_corte_mesa: string | null
  plan_sublimado_comment: string | null
  plan_decoration_comment: string | null
  plan_status_comment: string | null
  plan_leadtime_comment: string | null
  fragment_fabric_processed: ColumnType<Date, string | undefined, string | undefined> | null
  decoration_type: string | null
  void_reason: string | null
  rhinestone_estimate_total_rocks: number | null
  rhinestone_estimate_minutes: number | null
  rhinestone_estimate_calc: number | null
  garment_detail: string | null
  unit_price: number | null
  total_price: number | null
  manual_ship_datetime: ColumnType<Date, string | undefined, string | undefined> | null
  original_style_id: number | null
  address: string | null
  lettering: string | null
  is_rush: boolean
  is_remake: boolean
  is_priority: boolean
  is_packet_printed: boolean
}
