import { z } from 'zod'

// Work Repo Schemas (based on actual database structure)
export const createWorkRepoSchema = z.object({
  mo_id: z.number().int().positive().nullable().optional(),
  creator_email: z.string().email().max(150).nullable().optional(),
  work_repo_status_id: z.number().int().positive().nullable().optional(),
  affected_units: z.number().int().nullable().optional(),
  estimated_unit_equivalent: z.number().nullable().optional(),
  reported_work_area_id: z.number().int().positive().nullable().optional(),
  work_issue_id: z.number().int().positive().nullable().optional(),
  work_reason_note: z.string().nullable().optional(),
  found_work_voucher_id: z.number().int().positive().nullable().optional(),
  found_work_ticket_id: z.number().int().positive().nullable().optional(),
  customer_fault: z.boolean().nullable().optional(),
  charge_amount: z.number().nullable().optional(),
  estimated_material_cost: z.number().nullable().optional(),
  estimated_labor_cost: z.number().nullable().optional(),
  comments: z.string().nullable().optional(),
  item_comments: z.string().nullable().optional(),
  services_comments: z.string().nullable().optional(),
  material_comments: z.string().nullable().optional(),
  approval_date: z.string().datetime().nullable().optional(),
  approval_user: z.string().max(50).nullable().optional(),
  materials_approver_email: z.string().email().max(200).nullable().optional(),
  materials_approve_date: z.string().datetime().nullable().optional(),
  in_production_date: z.string().datetime().nullable().optional(),
  finish_date: z.string().datetime().nullable().optional(),
  repo_type: z.string().max(50).nullable().optional(),
  paper_yds: z.number().nullable().optional(),
  last_status_changed: z.string().datetime().nullable().optional(),
  warehouse_materials_preparred: z.boolean().nullable().optional(),
  warehouse_materials_preparred_date: z
    .string()
    .datetime()
    .nullable()
    .optional(),
  warehouse_materials_given_email: z
    .string()
    .email()
    .max(200)
    .nullable()
    .optional(),
  printer_use: z.string().max(200).nullable().optional(),
  is_customer_charged: z.boolean().nullable().optional(),
  customer_charge_invoice_number: z.string().max(250).nullable().optional(),
  customer_charge_invoice_date: z.string().datetime().nullable().optional(),
})

export const updateWorkRepoSchema = createWorkRepoSchema.partial()

// Work Repo Material Schemas (based on actual database structure)
export const createWorkRepoMaterialSchema = z.object({
  work_repo_id: z.number().int().positive(),
  part_number: z.string().max(100).nullable().optional(),
  unit_of_measure: z.string().max(20).nullable().optional(),
  quantity: z.number().nullable().optional(),
})

export const updateWorkRepoMaterialSchema = createWorkRepoMaterialSchema
  .partial()
  .omit({ work_repo_id: true })

// Work Repo Piece Schemas (based on actual database structure)
export const createWorkRepoPieceSchema = z.object({
  work_repo_id: z.number().int().positive(),
  piece: z.string().max(100).nullable().optional(),
  size: z.string().max(50).nullable().optional(),
  quantity: z.number().nullable().optional(),
})

export const updateWorkRepoPieceSchema = createWorkRepoPieceSchema
  .partial()
  .omit({ work_repo_id: true })

// Work Repo Status Schemas (based on actual database structure - lookup table)
export const createWorkRepoStatusSchema = z.object({
  sort: z.number().int().nullable().optional(),
  name: z.string().max(100).nullable().optional(),
  notifiy_emails: z.string().max(500).nullable().optional(),
  notify_creator: z.boolean().nullable().optional(),
  subject_line: z.string().max(200).nullable().optional(),
  work_template_id: z.number().int().positive().nullable().optional(),
})

export const updateWorkRepoStatusSchema = createWorkRepoStatusSchema.partial()

// Work Repo Note Schemas (based on actual database structure)
export const createWorkRepoNoteSchema = z.object({
  work_repo_id: z.number().int().positive(),
  user_mail: z.string().email().max(150).nullable().optional(),
  note: z.string().nullable().optional(),
})

export const updateWorkRepoNoteSchema = createWorkRepoNoteSchema
  .partial()
  .omit({ work_repo_id: true })

// Query parameter schemas (based on actual database structure)
export const repoQuerySchema = z.object({
  work_repo_status_id: z.coerce.number().int().positive().optional(),
  creator_email: z.string().email().optional(),
  repo_type: z.string().optional(),
  mo_id: z.coerce.number().int().positive().optional(),
  search: z.string().optional(),
  limit: z.coerce.number().int().positive().max(100).default(50),
  offset: z.coerce.number().int().nonnegative().default(0),
})

export const materialQuerySchema = z.object({
  part_number: z.string().optional(),
  unit_of_measure: z.string().optional(),
  limit: z.coerce.number().int().positive().max(100).default(50),
  offset: z.coerce.number().int().nonnegative().default(0),
})

export const pieceQuerySchema = z.object({
  piece: z.string().optional(),
  size: z.string().optional(),
  limit: z.coerce.number().int().positive().max(100).default(50),
  offset: z.coerce.number().int().nonnegative().default(0),
})

export const statusQuerySchema = z.object({
  name: z.string().optional(),
  work_template_id: z.coerce.number().int().positive().optional(),
  limit: z.coerce.number().int().positive().max(100).default(50),
  offset: z.coerce.number().int().nonnegative().default(0),
})

export const noteQuerySchema = z.object({
  user_mail: z.string().email().optional(),
  limit: z.coerce.number().int().positive().max(100).default(50),
  offset: z.coerce.number().int().nonnegative().default(0),
})

// ID parameter schema
export const idParamSchema = z.object({
  id: z.coerce.number().int().positive(),
})

// Export types for use in controllers
export type CreateWorkRepoInput = z.infer<typeof createWorkRepoSchema>
export type UpdateWorkRepoInput = z.infer<typeof updateWorkRepoSchema>
export type CreateWorkRepoMaterialInput = z.infer<
  typeof createWorkRepoMaterialSchema
>
export type UpdateWorkRepoMaterialInput = z.infer<
  typeof updateWorkRepoMaterialSchema
>
export type CreateWorkRepoPieceInput = z.infer<typeof createWorkRepoPieceSchema>
export type UpdateWorkRepoPieceInput = z.infer<typeof updateWorkRepoPieceSchema>
export type CreateWorkRepoStatusInput = z.infer<
  typeof createWorkRepoStatusSchema
>
export type UpdateWorkRepoStatusInput = z.infer<
  typeof updateWorkRepoStatusSchema
>
export type CreateWorkRepoNoteInput = z.infer<typeof createWorkRepoNoteSchema>
export type UpdateWorkRepoNoteInput = z.infer<typeof updateWorkRepoNoteSchema>
export type RepoQueryInput = z.infer<typeof repoQuerySchema>
export type MaterialQueryInput = z.infer<typeof materialQuerySchema>
export type PieceQueryInput = z.infer<typeof pieceQuerySchema>
export type StatusQueryInput = z.infer<typeof statusQuerySchema>
export type NoteQueryInput = z.infer<typeof noteQuerySchema>
export type IdParamInput = z.infer<typeof idParamSchema>
