import { Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import { repoService } from '../services/repoService'

export const getHomePage = async (_req: Request, res: Response) => {
  try {
    // Get repository statistics
    const [
      allReposResult,
      allStatuses,
      totalMaterials,
      totalPieces,
      totalNotes,
    ] = await Promise.all([
      repoService.getAllRepos(100, 0), // Get first 100 for stats
      repoService.getAllStatuses(),
      // Get a sample of materials to count (we'll count all repos' materials)
      getAllMaterialsCount(),
      getAllPiecesCount(),
      getAllNotesCount(),
    ])

    const { data: allRepos, total: totalRepos } = allReposResult

    // Calculate statistics
    const stats = {
      totalRepos: totalRepos, // Use the actual total count from database
      totalStatuses: allStatuses.length,
      totalMaterials,
      totalPieces,
      totalNotes,
      reposByStatus: await getReposByStatusStats(),
      recentRepos: allRepos
        .sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )
        .slice(0, 5),
      reposByType: getReposByType(allRepos),
      costSummary: getCostSummary(allRepos),
    }

    // Create HTML response
    const html = generateHomePage(stats)

    res.status(StatusCodes.OK).send(html)
  } catch (error) {
    console.error('Error generating home page:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).send(`
      <html>
        <head><title>Repository Tracking - Error</title></head>
        <body>
          <h1>Error Loading Home Page</h1>
          <p>Unable to load repository statistics: ${error instanceof Error ? error.message : 'Unknown error'}</p>
        </body>
      </html>
    `)
  }
}

// Helper functions to get counts
async function getAllMaterialsCount(): Promise<number> {
  try {
    const allReposResult = await repoService.getAllRepos(100, 0) // Get first 100 for counting
    const allRepos = allReposResult.data
    let totalMaterials = 0

    for (const repo of allRepos) {
      const materials = await repoService.getMaterialsByRepoId(repo.id)
      totalMaterials += materials.length
    }

    return totalMaterials
  } catch (error) {
    console.error('Error counting materials:', error)
    return 0
  }
}

async function getAllPiecesCount(): Promise<number> {
  try {
    const allReposResult = await repoService.getAllRepos(100, 0) // Get first 100 for counting
    const allRepos = allReposResult.data
    let totalPieces = 0

    for (const repo of allRepos) {
      const pieces = await repoService.getPiecesByRepoId(repo.id)
      totalPieces += pieces.length
    }

    return totalPieces
  } catch (error) {
    console.error('Error counting pieces:', error)
    return 0
  }
}

async function getAllNotesCount(): Promise<number> {
  try {
    const allReposResult = await repoService.getAllRepos(100, 0) // Get first 100 for counting
    const allRepos = allReposResult.data
    let totalNotes = 0

    for (const repo of allRepos) {
      const notes = await repoService.getNotesByRepoId(repo.id)
      totalNotes += notes.length
    }

    return totalNotes
  } catch (error) {
    console.error('Error counting notes:', error)
    return 0
  }
}

async function getReposByStatusStats() {
  try {
    const [allReposResult, allStatuses] = await Promise.all([
      repoService.getAllRepos(100, 0), // Get first 100 for stats
      repoService.getAllStatuses(),
    ])
    const allRepos = allReposResult.data

    const statusStats: { [key: string]: number } = {}

    // Initialize all statuses with 0
    allStatuses.forEach((status) => {
      statusStats[status.name || 'Unknown'] = 0
    })

    // Count repos by status
    for (const repo of allRepos) {
      if (repo.work_repo_status_id) {
        const status = await repoService.getStatusById(repo.work_repo_status_id)
        const statusName = status?.name || 'Unknown'
        statusStats[statusName] = (statusStats[statusName] || 0) + 1
      } else {
        statusStats['No Status'] = (statusStats['No Status'] || 0) + 1
      }
    }

    return statusStats
  } catch (error) {
    console.error('Error getting repos by status:', error)
    return {}
  }
}

function getReposByType(repos: any[]) {
  const typeStats: { [key: string]: number } = {}

  repos.forEach((repo) => {
    const type = repo.repo_type || 'Unknown'
    typeStats[type] = (typeStats[type] || 0) + 1
  })

  return typeStats
}

function getCostSummary(repos: any[]) {
  let totalEstimatedMaterialCost = 0
  let totalEstimatedLaborCost = 0
  let totalChargeAmount = 0

  repos.forEach((repo) => {
    totalEstimatedMaterialCost += repo.estimated_material_cost || 0
    totalEstimatedLaborCost += repo.estimated_labor_cost || 0
    totalChargeAmount += repo.charge_amount || 0
  })

  return {
    totalEstimatedMaterialCost,
    totalEstimatedLaborCost,
    totalChargeAmount,
    totalEstimatedCost: totalEstimatedMaterialCost + totalEstimatedLaborCost,
  }
}

function generateHomePage(stats: any): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Repository Tracking System - Home</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        .nav-brand {
            color: white;
            font-size: 1.5em;
            font-weight: bold;
            text-decoration: none;
            padding: 15px 0;
        }
        .nav-links {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        .nav-links li {
            margin: 0;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 20px 20px;
            display: block;
            transition: background-color 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        .nav-links a:hover {
            background-color: rgba(255,255,255,0.1);
            border-bottom-color: rgba(255,255,255,0.5);
        }
        .nav-links a.active {
            background-color: rgba(255,255,255,0.2);
            border-bottom-color: white;
        }
        .main-content {
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin: 0;
        }
        .stat-label {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .section {
            padding: 30px;
            border-top: 1px solid #eee;
        }
        .section h2 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.5em;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .status-list, .type-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .status-list li, .type-list li {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .recent-repos {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .recent-repos li {
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #28a745;
        }
        .repo-id {
            font-weight: bold;
            color: #667eea;
        }
        .repo-details {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        .cost-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .cost-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .cost-amount {
            font-size: 1.5em;
            font-weight: bold;
            color: #28a745;
        }
        .api-links {
            background: #f8f9fa;
            padding: 20px;
            margin-top: 20px;
            border-radius: 6px;
        }
        .api-links h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .api-links a {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 15px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .api-links a:hover {
            background: #5a6fd8;
        }
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="nav-brand">Reposition Tracker</a>
            <ul class="nav-links">
                <li><a href="/" class="active">Home</a></li>
                <li><a href="/create-repo">Create Reposition</a></li>
                <li><a href="/active-repos">Active Repositions</a></li>
                <li><a href="/prepare-repos">Prepare Repositions</a></li>
            </ul>
        </div>
    </nav>

    <div class="main-content">
        <div class="container">
        <div class="header">
            <h1>Reposition Tracking System</h1>
            <p>Manufacturing Order Rework Management Dashboard</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">${stats.totalRepos}</div>
                <div class="stat-label">Total Repositions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.totalMaterials}</div>
                <div class="stat-label">Total Materials</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.totalPieces}</div>
                <div class="stat-label">Total Pieces</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.totalNotes}</div>
                <div class="stat-label">Total Notes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.totalStatuses}</div>
                <div class="stat-label">Status Types</div>
            </div>
        </div>

        <div class="section">
            <h2>Cost Summary</h2>
            <div class="cost-grid">
                <div class="cost-item">
                    <div class="cost-amount">$${stats.costSummary.totalEstimatedMaterialCost.toLocaleString()}</div>
                    <div>Estimated Material Cost</div>
                </div>
                <div class="cost-item">
                    <div class="cost-amount">$${stats.costSummary.totalEstimatedLaborCost.toLocaleString()}</div>
                    <div>Estimated Labor Cost</div>
                </div>
                <div class="cost-item">
                    <div class="cost-amount">$${stats.costSummary.totalChargeAmount.toLocaleString()}</div>
                    <div>Total Charge Amount</div>
                </div>
                <div class="cost-item">
                    <div class="cost-amount">$${stats.costSummary.totalEstimatedCost.toLocaleString()}</div>
                    <div>Total Estimated Cost</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="two-column">
                <div>
                    <h2>Repositions by Status</h2>
                    <ul class="status-list">
                        ${Object.entries(stats.reposByStatus)
                          .map(
                            ([status, count]) =>
                              `<li><span>${status}</span><span>${count}</span></li>`
                          )
                          .join('')}
                    </ul>
                </div>
                <div>
                    <h2>Repositions by Type</h2>
                    <ul class="type-list">
                        ${Object.entries(stats.reposByType)
                          .map(
                            ([type, count]) =>
                              `<li><span>${type}</span><span>${count}</span></li>`
                          )
                          .join('')}
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Recent Repositions</h2>
            <ul class="recent-repos">
                ${stats.recentRepos
                  .map(
                    (repo: any) => `
                    <li>
                        <div class="repo-id">Reposition #${repo.id}</div>
                        <div class="repo-details">
                            ${repo.creator_email ? `Creator: ${repo.creator_email}` : 'No creator specified'} |
                            ${repo.repo_type ? `Type: ${repo.repo_type}` : 'No type specified'} |
                            Created: ${new Date(repo.created_at).toLocaleDateString()}
                        </div>
                    </li>
                `
                  )
                  .join('')}
            </ul>
        </div>

        <div class="section">
            <div class="api-links">
                <h3>API Endpoints</h3>
                <a href="/api/v1/repos">All Repositions</a>
                <a href="/api/v1/repos/statuses">Status Definitions</a>
                <a href="/api/v1">API v1 Info</a>
            </div>
        </div>
    </div>
    </div>
</body>
</html>
  `
}
