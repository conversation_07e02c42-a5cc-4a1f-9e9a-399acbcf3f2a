import express, { Request, Response } from 'express'
import { getHello } from '@app/controllers/test'
import repoRoutes from './repos'
import materialRoutes from './materials'

export const v1Router = express.Router()

v1Router.get('/', (_req: Request, res: Response) => {
  res.send('API v1')
})

v1Router.get('/hello', getHello)

// Repo management routes
v1Router.use('/repos', repoRoutes)

// Material search routes
v1Router.use('/materials', materialRoutes)
