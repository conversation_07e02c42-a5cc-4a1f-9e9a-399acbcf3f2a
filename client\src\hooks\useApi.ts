import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiService } from '../services/api'

// Query Keys
export const queryKeys = {
  apiRoot: ['api', 'root'] as const,
  v1Root: ['api', 'v1', 'root'] as const,
  hello: ['api', 'v1', 'hello'] as const,
  orders: ['api', 'v1', 'orders'] as const,
  orderDetails: ['api', 'v1', 'orders', 'details'] as const,
}

// API Root Query
export const useApiRoot = () => {
  return useQuery({
    queryKey: queryKeys.apiRoot,
    queryFn: apiService.getApiRoot,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// V1 Root Query
export const useV1Root = () => {
  return useQuery({
    queryKey: queryKeys.v1Root,
    queryFn: apiService.getV1Root,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Hello Endpoint Query
export const useHello = (params?: any, enabled: boolean = true) => {
  return useQuery({
    queryKey: [...queryKeys.hello, params],
    queryFn: () => apiService.getHello(params),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Order Details Query
export const useOrderDetails = (enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.orderDetails,
    queryFn: apiService.getOrderDetails,
    enabled,
    staleTime: 2 * 60 * 1000, // 2 minutes - orders change more frequently
    retry: (failureCount, error: any) => {
      // Don't retry on auth errors
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false
      }
      return failureCount < 2
    },
  })
}

// Generic API Query Hook
export const useApiQuery = (
  queryKey: string[],
  queryFn: () => Promise<any>,
  options?: {
    enabled?: boolean
    staleTime?: number
    retry?: boolean | number | ((failureCount: number, error: any) => boolean)
  }
) => {
  return useQuery({
    queryKey,
    queryFn,
    enabled: options?.enabled ?? true,
    staleTime: options?.staleTime ?? 5 * 60 * 1000,
    retry: options?.retry ?? 3,
  })
}

// Generic API Mutation Hook
export const useApiMutation = (
  mutationFn: (variables: any) => Promise<any>,
  options?: {
    onSuccess?: (data: any, variables: any) => void
    onError?: (error: any, variables: any) => void
    invalidateQueries?: string[][]
  }
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn,
    onSuccess: (data, variables) => {
      // Invalidate specified queries
      if (options?.invalidateQueries) {
        options.invalidateQueries.forEach((queryKey) => {
          queryClient.invalidateQueries({ queryKey })
        })
      }
      options?.onSuccess?.(data, variables)
    },
    onError: options?.onError,
  })
}

// Utility hook to invalidate queries
export const useInvalidateQueries = () => {
  const queryClient = useQueryClient()

  return {
    invalidateAll: () => queryClient.invalidateQueries(),
    invalidateApiRoot: () => queryClient.invalidateQueries({ queryKey: queryKeys.apiRoot }),
    invalidateV1Root: () => queryClient.invalidateQueries({ queryKey: queryKeys.v1Root }),
    invalidateHello: () => queryClient.invalidateQueries({ queryKey: queryKeys.hello }),
    invalidateOrders: () => queryClient.invalidateQueries({ queryKey: queryKeys.orders }),
    invalidateOrderDetails: () => queryClient.invalidateQueries({ queryKey: queryKeys.orderDetails }),
  }
}

// Hook to manage API key
export const useApiKey = () => {
  const queryClient = useQueryClient()

  const setApiKey = (token: string) => {
    localStorage.setItem('varpro-token', token)
    // Invalidate all queries when API key changes
    queryClient.invalidateQueries()
  }

  const removeApiKey = () => {
    localStorage.removeItem('varpro-token')
    // Clear all queries when API key is removed
    queryClient.clear()
  }

  const getApiKey = () => {
    return localStorage.getItem('varpro-token')
  }

  return {
    setApiKey,
    removeApiKey,
    getApiKey,
    hasApiKey: !!getApiKey(),
  }
}
