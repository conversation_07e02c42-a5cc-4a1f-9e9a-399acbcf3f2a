import { ColumnType, Generated, JSONColumnType } from 'kysely'

export interface ApiRequestTable {
  id: Generated<number>
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, never>
  request_uuid: string
  ip_address: string
  method: string
  url: string
  headers: JSONColumnType<any>
  body: JSONColumnType<any> | null
  response: JSONColumnType<any> | null
  status_code: number | null
  duration_ms: number | null
  customer_app_id: number | null
}
