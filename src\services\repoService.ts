import { db } from '../db/database'
import {
  WorkRepo,
  NewWorkRepo,
  WorkRepoUpdate,
  WorkRepoMaterial,
  NewWorkRepoMaterial,
  WorkRepoMaterialUpdate,
  WorkRepoPiece,
  NewWorkRepoPiece,
  WorkRepoPieceUpdate,
  WorkRepoStatus,
  NewWorkRepoStatus,
  WorkRepoStatusUpdate,
  WorkRepoNote,
  NewWorkRepoNote,
  WorkRepoNoteUpdate,
} from '../db/models'

export class RepoService {
  // ===== WORK REPOS =====

  async getAllRepos(
    limit: number = 100,
    offset: number = 0
  ): Promise<{ data: WorkRepo[]; total: number; hasMore: boolean }> {
    const actualLimit = Math.min(limit, 100) // Max 100 results

    const [data, countResult] = await Promise.all([
      db
        .selectFrom('work_repos')
        .selectAll()
        .orderBy('created_at', 'desc')
        .limit(actualLimit)
        .offset(offset)
        .execute(),
      db
        .selectFrom('work_repos')
        .select(db.fn.count('id').as('count'))
        .executeTakeFirst(),
    ])

    const total = Number(countResult?.count || 0)
    const hasMore = offset + actualLimit < total

    return { data, total, hasMore }
  }

  async getRepoById(id: number): Promise<WorkRepo | undefined> {
    return await db
      .selectFrom('work_repos')
      .selectAll()
      .where('id', '=', id)
      .executeTakeFirst()
  }

  async getReposByStatusId(statusId: number): Promise<WorkRepo[]> {
    return await db
      .selectFrom('work_repos')
      .selectAll()
      .where('work_repo_status_id', '=', statusId)
      .execute()
  }

  async createRepo(
    repoData: NewWorkRepo & { materials_data?: string }
  ): Promise<WorkRepo> {
    // Extract materials data if provided
    const { materials_data, ...repoFields } = repoData

    // Create the repo first
    const [createdRepo] = await db
      .insertInto('work_repos')
      .values(repoFields)
      .returningAll()
      .execute()

    // If materials data is provided, save to work_repo_materials table
    if (materials_data) {
      try {
        const materials = JSON.parse(materials_data)
        if (Array.isArray(materials) && materials.length > 0) {
          const materialInserts = materials.map((material) => ({
            work_repo_id: createdRepo.id,
            part_number: material.part_number,
            unit_of_measure: material.unit_of_measure,
            quantity: material.quantity,
          }))

          await db
            .insertInto('work_repo_materials')
            .values(materialInserts)
            .execute()
        }
      } catch (error) {
        console.error('Error parsing materials data:', error)
      }
    }

    return createdRepo
  }

  async updateRepo(
    id: number,
    updates: WorkRepoUpdate
  ): Promise<WorkRepo | undefined> {
    const [updatedRepo] = await db
      .updateTable('work_repos')
      .set(updates)
      .where('id', '=', id)
      .returningAll()
      .execute()
    return updatedRepo
  }

  async deleteRepo(id: number): Promise<boolean> {
    const result = await db
      .deleteFrom('work_repos')
      .where('id', '=', id)
      .executeTakeFirst()
    return result.numDeletedRows > 0
  }

  // ===== WORK REPO MATERIALS =====

  async getMaterialsByRepoId(repoId: number): Promise<WorkRepoMaterial[]> {
    return await db
      .selectFrom('work_repo_materials')
      .selectAll()
      .where('work_repo_id', '=', repoId)
      .execute()
  }

  async getMaterialById(id: number): Promise<WorkRepoMaterial | undefined> {
    return await db
      .selectFrom('work_repo_materials')
      .selectAll()
      .where('id', '=', id)
      .executeTakeFirst()
  }

  async createMaterial(
    material: NewWorkRepoMaterial
  ): Promise<WorkRepoMaterial> {
    const [createdMaterial] = await db
      .insertInto('work_repo_materials')
      .values(material)
      .returningAll()
      .execute()
    return createdMaterial
  }

  async updateMaterial(
    id: number,
    updates: WorkRepoMaterialUpdate
  ): Promise<WorkRepoMaterial | undefined> {
    const [updatedMaterial] = await db
      .updateTable('work_repo_materials')
      .set(updates)
      .where('id', '=', id)
      .returningAll()
      .execute()
    return updatedMaterial
  }

  async deleteMaterial(id: number): Promise<boolean> {
    const result = await db
      .deleteFrom('work_repo_materials')
      .where('id', '=', id)
      .executeTakeFirst()
    return result.numDeletedRows > 0
  }

  // ===== WORK REPO PIECES =====

  async getPiecesByRepoId(repoId: number): Promise<WorkRepoPiece[]> {
    return await db
      .selectFrom('work_repo_pieces')
      .selectAll()
      .where('work_repo_id', '=', repoId)
      .execute()
  }

  async getPieceById(id: number): Promise<WorkRepoPiece | undefined> {
    return await db
      .selectFrom('work_repo_pieces')
      .selectAll()
      .where('id', '=', id)
      .executeTakeFirst()
  }

  async createPiece(piece: NewWorkRepoPiece): Promise<WorkRepoPiece> {
    const [createdPiece] = await db
      .insertInto('work_repo_pieces')
      .values(piece)
      .returningAll()
      .execute()
    return createdPiece
  }

  async updatePiece(
    id: number,
    updates: WorkRepoPieceUpdate
  ): Promise<WorkRepoPiece | undefined> {
    const [updatedPiece] = await db
      .updateTable('work_repo_pieces')
      .set(updates)
      .where('id', '=', id)
      .returningAll()
      .execute()
    return updatedPiece
  }

  async deletePiece(id: number): Promise<boolean> {
    const result = await db
      .deleteFrom('work_repo_pieces')
      .where('id', '=', id)
      .executeTakeFirst()
    return result.numDeletedRows > 0
  }

  // ===== WORK REPO STATUSES (Lookup Table) =====

  async getAllStatuses(): Promise<WorkRepoStatus[]> {
    return await db
      .selectFrom('work_repo_statuses')
      .selectAll()
      .orderBy('sort', 'asc')
      .execute()
  }

  async getStatusById(statusId: number): Promise<WorkRepoStatus | undefined> {
    return await db
      .selectFrom('work_repo_statuses')
      .selectAll()
      .where('id', '=', statusId)
      .executeTakeFirst()
  }

  async createStatus(status: NewWorkRepoStatus): Promise<WorkRepoStatus> {
    const [createdStatus] = await db
      .insertInto('work_repo_statuses')
      .values(status)
      .returningAll()
      .execute()
    return createdStatus
  }

  async updateStatus(
    id: number,
    updates: WorkRepoStatusUpdate
  ): Promise<WorkRepoStatus | undefined> {
    const [updatedStatus] = await db
      .updateTable('work_repo_statuses')
      .set(updates)
      .where('id', '=', id)
      .returningAll()
      .execute()
    return updatedStatus
  }

  async deleteStatus(id: number): Promise<boolean> {
    const result = await db
      .deleteFrom('work_repo_statuses')
      .where('id', '=', id)
      .executeTakeFirst()
    return result.numDeletedRows > 0
  }

  // ===== WORK REPO NOTES =====

  async getNotesByRepoId(repoId: number): Promise<WorkRepoNote[]> {
    return await db
      .selectFrom('work_repo_notes')
      .selectAll()
      .where('work_repo_id', '=', repoId)
      .orderBy('created_at', 'desc')
      .execute()
  }

  async getNoteById(id: number): Promise<WorkRepoNote | undefined> {
    return await db
      .selectFrom('work_repo_notes')
      .selectAll()
      .where('id', '=', id)
      .executeTakeFirst()
  }

  async getNotesByUserMail(userMail: string): Promise<WorkRepoNote[]> {
    return await db
      .selectFrom('work_repo_notes')
      .selectAll()
      .where('user_mail', '=', userMail)
      .orderBy('created_at', 'desc')
      .execute()
  }

  async createNote(note: NewWorkRepoNote): Promise<WorkRepoNote> {
    const [createdNote] = await db
      .insertInto('work_repo_notes')
      .values(note)
      .returningAll()
      .execute()
    return createdNote
  }

  async updateNote(
    id: number,
    updates: WorkRepoNoteUpdate
  ): Promise<WorkRepoNote | undefined> {
    const [updatedNote] = await db
      .updateTable('work_repo_notes')
      .set(updates)
      .where('id', '=', id)
      .returningAll()
      .execute()
    return updatedNote
  }

  async deleteNote(id: number): Promise<boolean> {
    const result = await db
      .deleteFrom('work_repo_notes')
      .where('id', '=', id)
      .executeTakeFirst()
    return result.numDeletedRows > 0
  }

  // ===== COMPLEX QUERIES =====

  async getRepoWithAllData(repoId: number): Promise<{
    repo: WorkRepo | undefined
    materials: WorkRepoMaterial[]
    pieces: WorkRepoPiece[]
    notes: WorkRepoNote[]
    status?: WorkRepoStatus
  }> {
    const [repo, materials, pieces, notes] = await Promise.all([
      this.getRepoById(repoId),
      this.getMaterialsByRepoId(repoId),
      this.getPiecesByRepoId(repoId),
      this.getNotesByRepoId(repoId),
    ])

    // Get the status if the repo has a status_id
    let status: WorkRepoStatus | undefined
    if (repo?.work_repo_status_id) {
      status = await this.getStatusById(repo.work_repo_status_id)
    }

    return {
      repo,
      materials,
      pieces,
      notes,
      status,
    }
  }

  async getRepoSummary(repoId: number): Promise<{
    repo: WorkRepo | undefined
    materialCount: number
    pieceCount: number
    noteCount: number
    status?: WorkRepoStatus
  }> {
    const [repo, materials, pieces, notes] = await Promise.all([
      this.getRepoById(repoId),
      this.getMaterialsByRepoId(repoId),
      this.getPiecesByRepoId(repoId),
      this.getNotesByRepoId(repoId),
    ])

    // Get the status if the repo has a status_id
    let status: WorkRepoStatus | undefined
    if (repo?.work_repo_status_id) {
      status = await this.getStatusById(repo.work_repo_status_id)
    }

    return {
      repo,
      materialCount: materials.length,
      pieceCount: pieces.length,
      noteCount: notes.length,
      status,
    }
  }

  async searchRepos(searchTerm: string): Promise<WorkRepo[]> {
    return await db
      .selectFrom('work_repos')
      .selectAll()
      .where((eb) =>
        eb.or([
          eb('creator_email', 'like', `%${searchTerm}%`),
          eb('work_reason_note', 'like', `%${searchTerm}%`),
          eb('comments', 'like', `%${searchTerm}%`),
          eb('item_comments', 'like', `%${searchTerm}%`),
          eb('services_comments', 'like', `%${searchTerm}%`),
          eb('material_comments', 'like', `%${searchTerm}%`),
        ])
      )
      .execute()
  }

  async getReposByCreatorEmail(creatorEmail: string): Promise<WorkRepo[]> {
    return await db
      .selectFrom('work_repos')
      .selectAll()
      .where('creator_email', '=', creatorEmail)
      .execute()
  }

  async getReposByType(repoType: string): Promise<WorkRepo[]> {
    return await db
      .selectFrom('work_repos')
      .selectAll()
      .where('repo_type', '=', repoType)
      .execute()
  }

  async getReposByMoId(moId: number): Promise<WorkRepo[]> {
    return await db
      .selectFrom('work_repos')
      .selectAll()
      .where('mo_id', '=', moId)
      .orderBy('created_at', 'desc')
      .execute()
  }

  async getPreviousReposByMoId(moId: number): Promise<WorkRepo[]> {
    return await db
      .selectFrom('work_repos')
      .selectAll()
      .where('mo_id', '=', moId)
      .orderBy('created_at', 'desc')
      .execute()
  }
}

// Export a singleton instance
export const repoService = new RepoService()
