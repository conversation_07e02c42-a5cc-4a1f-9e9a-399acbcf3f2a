import { config } from 'dotenv'
import { Database } from './models' // this is the Database interface we defined earlier
import { <PERSON>ys<PERSON>, MysqlDialect } from 'kysely'
import { createPool } from 'mysql2'

config({ path: __dirname + `/../../.env` })

const dialect = new MysqlDialect({
  pool: createPool({
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASS,
    port: Number(process.env.DB_PORT) || 3306,
    connectionLimit: 10,
  }),
})

// Database interface is passed to <PERSON><PERSON><PERSON>'s constructor, and from now on, <PERSON><PERSON><PERSON>
// knows your database structure.
// Dialect is passed to <PERSON><PERSON><PERSON>'s constructor, and from now on, <PERSON><PERSON><PERSON> knows how
// to communicate with your database.
export const db = new Kysely<Database>({
  dialect,
})
