import React, { useState } from 'react'
import { useApiRoot, useV1Root, useHello, useOrderDetails, useApi<PERSON>ey } from '../hooks/useApi'

const ApiTest: React.FC = () => {
  const [selectedEndpoint, setSelectedEndpoint] = useState<string | null>(null)
  const { hasApiKey } = useApiKey()

  // React Query hooks for different endpoints
  const apiRootQuery = useApiRoot()
  const v1RootQuery = useV1Root()
  const helloQuery = useHello(undefined, false) // disabled by default
  const orderDetailsQuery = useOrderDetails(false) // disabled by default

  const endpoints = [
    {
      name: 'API Root',
      description: 'Test the main API endpoint',
      query: apiRootQuery,
      refetch: () => apiRootQuery.refetch(),
    },
    {
      name: 'API v1 Root',
      description: 'Test the v1 API endpoint',
      query: v1RootQuery,
      refetch: () => v1RootQuery.refetch(),
    },
    {
      name: 'Hello Endpoint',
      description: 'Test the hello endpoint',
      query: helloQuery,
      refetch: () => helloQuery.refetch(),
    },
    {
      name: 'Order Details',
      description: 'Test the order details endpoint (requires API key)',
      query: orderDetailsQuery,
      refetch: () => orderDetailsQuery.refetch(),
      requiresAuth: true,
    },
  ]

  const testEndpoint = (endpoint: any) => {
    setSelectedEndpoint(endpoint.name)
    endpoint.refetch()
  }

  const getQueryResult = (endpointName: string) => {
    const endpoint = endpoints.find(e => e.name === endpointName)
    return endpoint?.query
  }

  const currentQuery = selectedEndpoint ? getQueryResult(selectedEndpoint) : null

  return (
    <div className="page">
      <div className="page-header">
        <h1>API Testing</h1>
        <p className="page-description">
          Test different API endpoints to verify connectivity and functionality.
        </p>
      </div>

      <div className="endpoints-section">
        <h2>Available Endpoints</h2>
        <div className="endpoints-grid">
          {endpoints.map((endpoint) => (
            <div key={endpoint.name} className="endpoint-card">
              <h3>{endpoint.name}</h3>
              <p>{endpoint.description}</p>
              {endpoint.requiresAuth && !hasApiKey && (
                <p className="auth-warning">⚠️ Requires API key (set in Orders page)</p>
              )}
              <button
                onClick={() => testEndpoint(endpoint)}
                disabled={endpoint.query.isFetching || (endpoint.requiresAuth && !hasApiKey)}
                className="btn btn-secondary"
              >
                {endpoint.query.isFetching ? 'Testing...' : 'Test Endpoint'}
              </button>
              {endpoint.query.isSuccess && selectedEndpoint === endpoint.name && (
                <div className="query-status success">✅ Success</div>
              )}
              {endpoint.query.isError && selectedEndpoint === endpoint.name && (
                <div className="query-status error">❌ Error</div>
              )}
            </div>
          ))}
        </div>
      </div>

      {currentQuery && selectedEndpoint && (
        <div className="response-section">
          <h2>Response for {selectedEndpoint}</h2>

          {currentQuery.isFetching && (
            <div className="loading">
              <p>Testing endpoint...</p>
            </div>
          )}

          {currentQuery.isError && (
            <div className="error-response">
              <h3>❌ Error Response</h3>
              <p><strong>Message:</strong> {currentQuery.error?.message || 'Unknown error'}</p>
              <div className="response-data">
                <h4>Error Details:</h4>
                <pre>{JSON.stringify(currentQuery.error, null, 2)}</pre>
              </div>
            </div>
          )}

          {currentQuery.isSuccess && (
            <div className="success-response">
              <h3>✅ Success Response</h3>
              <div className="response-data">
                <h4>Response Data:</h4>
                <pre>{JSON.stringify(currentQuery.data, null, 2)}</pre>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="api-info">
        <h2>API Information</h2>
        <p>
          <strong>Base URL (Development):</strong> http://localhost:3000/api
        </p>
        <p>
          <strong>Base URL (Production):</strong> /api
        </p>
        <p>
          <strong>Authentication:</strong> Use the x-varpro-company-token header for authenticated endpoints
        </p>
      </div>
    </div>
  )
}

export default ApiTest
