# Varpro Customer Portal - React Client

This is the React frontend client for the Varpro Customer Portal, built with Vite, React Router, and Axios.

## Features

- **React 19** with TypeScript
- **React Router** for client-side routing
- **React Query (TanStack Query)** for state management and API caching
- **Axios** for HTTP requests (used internally by React Query)
- **Vite** for fast development and building
- **Responsive design** with modern CSS
- **React Query DevTools** for debugging and monitoring

## Pages

1. **Home** - Welcome page with feature overview
2. **Orders** - Order management with API key authentication
3. **API Test** - Test different API endpoints

## Development

### Prerequisites

- Node.js (v16 or higher)
- npm

### Installation

```bash
# Install dependencies
npm install
```

### Running the Development Server

```bash
# Start the development server
npm run dev
```

The client will be available at `http://localhost:5173`

### Building for Production

```bash
# Build the client
npm run build
```

The built files will be in the `dist/` directory.

## API Integration with React Query

The client uses React Query for efficient API state management:

- **Development**: `http://localhost:3000/api`
- **Production**: `/api` (served by the same server)

### React Query Benefits

- **Automatic caching**: API responses are cached and reused
- **Background refetching**: Data stays fresh automatically
- **Loading states**: Built-in loading, error, and success states
- **Optimistic updates**: UI updates immediately for better UX
- **Retry logic**: Automatic retries on network failures
- **DevTools**: Visual debugging with React Query DevTools

### Authentication

Some endpoints require authentication using the `x-varpro-company-token` header. You can set your API key in the Orders page, and it will be stored in localStorage and automatically included in requests.

### Available Hooks

- `useApiRoot()` - Test API root endpoint
- `useV1Root()` - Test API v1 root endpoint
- `useHello()` - Test hello endpoint
- `useOrderDetails()` - Fetch order details (requires auth)
- `useApiKey()` - Manage API key state

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally

## Project Structure

```
client/
├── public/          # Static assets
├── src/
│   ├── components/  # Reusable React components
│   ├── pages/       # Page components
│   ├── services/    # API service layer
│   ├── App.tsx      # Main app component
│   ├── main.tsx     # React entry point
│   └── index.css    # Global styles
├── index.html       # HTML template
├── vite.config.ts   # Vite configuration
└── package.json     # Dependencies and scripts
```

## API Endpoints

The client can interact with these server endpoints:

- `GET /api/` - API root
- `GET /api/v1/` - API v1 root
- `GET /api/v1/hello` - Hello endpoint
- `GET /api/v1/order/details` - Order details (requires auth)

## Styling

The application uses modern CSS with CSS custom properties (variables) for theming. The design is responsive and works on both desktop and mobile devices.

## Development Notes

- The Vite dev server includes a proxy configuration to forward `/api` requests to the backend server
- Hot module replacement (HMR) is enabled for fast development
- TypeScript is configured for strict type checking
