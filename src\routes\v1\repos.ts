import { Router } from 'express'
import * as repoController from '../../controllers/repoController'
import { schemaValidationMiddleware } from '../../middleware/schemaValidationMiddleware'
import {
  createWorkRepoSchema,
  updateWorkRepoSchema,
  createWorkRepoMaterialSchema,
  updateWorkRepoMaterialSchema,
  createWorkRepoPieceSchema,
  updateWorkRepoPieceSchema,
  createWorkRepoStatusSchema,
  updateWorkRepoStatusSchema,
  createWorkRepoNoteSchema,
  updateWorkRepoNoteSchema,
  idParamSchema,
} from '../../zod/repoSchemas'

const router = Router()

// ===== WORK REPOS =====

// GET /api/v1/repos - Get all repositories with optional filtering
router.get('/', repoController.getAllRepos)

// GET /api/v1/repos/:id - Get repository by ID
router.get('/:id', repoController.getRepoById)

// GET /api/v1/repos/:id/full - Get repository with all related data
router.get('/:id/full', repoController.getRepoWithAllData)

// GET /api/v1/repos/:id/summary - Get repository summary
router.get('/:id/summary', repoController.getRepoSummary)

// POST /api/v1/repos - Create new repository
router.post(
  '/',
  schemaValidationMiddleware(createWorkRepoSchema),
  repoController.createRepo
)

// PUT /api/v1/repos/:id - Update repository
router.put(
  '/:id',
  schemaValidationMiddleware(updateWorkRepoSchema),
  repoController.updateRepo
)

// DELETE /api/v1/repos/:id - Delete repository
router.delete('/:id', repoController.deleteRepo)

// ===== WORK REPO MATERIALS =====

// GET /api/v1/repos/:id/materials - Get materials for a repository
router.get('/:id/materials', repoController.getMaterialsByRepoId)

// POST /api/v1/repos/materials - Create new material
router.post(
  '/materials',
  schemaValidationMiddleware(createWorkRepoMaterialSchema),
  repoController.createMaterial
)

// PUT /api/v1/repos/materials/:id - Update material
router.put(
  '/materials/:id',
  schemaValidationMiddleware(updateWorkRepoMaterialSchema),
  repoController.updateMaterial
)

// DELETE /api/v1/repos/materials/:id - Delete material
router.delete('/materials/:id', repoController.deleteMaterial)

// ===== WORK REPO PIECES =====

// GET /api/v1/repos/:id/pieces - Get pieces for a repository
router.get('/:id/pieces', repoController.getPiecesByRepoId)

// POST /api/v1/repos/pieces - Create new piece
router.post(
  '/pieces',
  schemaValidationMiddleware(createWorkRepoPieceSchema),
  repoController.createPiece
)

// PUT /api/v1/repos/pieces/:id - Update piece
router.put(
  '/pieces/:id',
  schemaValidationMiddleware(updateWorkRepoPieceSchema),
  repoController.updatePiece
)

// DELETE /api/v1/repos/pieces/:id - Delete piece
router.delete('/pieces/:id', repoController.deletePiece)

// ===== WORK REPO STATUSES (Lookup Table) =====

// GET /api/v1/repos/statuses - Get all statuses (lookup table)
router.get('/statuses', repoController.getAllStatuses)

// GET /api/v1/repos/statuses/:id - Get status by ID
router.get('/statuses/:id', repoController.getStatusById)

// POST /api/v1/repos/statuses - Create new status
router.post(
  '/statuses',
  schemaValidationMiddleware(createWorkRepoStatusSchema),
  repoController.createStatus
)

// ===== WORK REPO NOTES =====

// GET /api/v1/repos/:id/notes - Get notes for a repository
router.get('/:id/notes', repoController.getNotesByRepoId)

// POST /api/v1/repos/notes - Create new note
router.post(
  '/notes',
  schemaValidationMiddleware(createWorkRepoNoteSchema),
  repoController.createNote
)

// PUT /api/v1/repos/notes/:id - Update note
router.put(
  '/notes/:id',
  schemaValidationMiddleware(updateWorkRepoNoteSchema),
  repoController.updateNote
)

// DELETE /api/v1/repos/notes/:id - Delete note
router.delete('/notes/:id', repoController.deleteNote)

export default router
