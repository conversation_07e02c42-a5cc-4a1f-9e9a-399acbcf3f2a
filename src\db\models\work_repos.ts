import { ColumnType, Generated } from 'kysely'

// Main work repositories table
export interface WorkRepoTable {
  id: Generated<number>
  mo_id: number | null
  creator_email: string | null
  work_repo_status_id: number | null
  affected_units: number | null
  estimated_unit_equivalent: number | null
  reported_work_area_id: number | null
  work_issue_id: number | null
  work_reason_note: string | null
  found_work_voucher_id: number | null
  found_work_ticket_id: number | null
  customer_fault: boolean | null
  charge_amount: number | null
  estimated_material_cost: number | null
  estimated_labor_cost: number | null
  comments: string | null
  item_comments: string | null
  services_comments: string | null
  material_comments: string | null
  approval_date: ColumnType<Date, string | undefined, string | undefined> | null
  approval_user: string | null
  materials_approver_email: string | null
  materials_approve_date: ColumnType<
    Date,
    string | undefined,
    string | undefined
  > | null
  in_production_date: ColumnType<
    Date,
    string | undefined,
    string | undefined
  > | null
  finish_date: ColumnType<Date, string | undefined, string | undefined> | null
  repo_type: string | null
  paper_yds: number | null
  last_status_changed: ColumnType<
    Date,
    string | undefined,
    string | undefined
  > | null
  warehouse_materials_preparred: boolean | null
  warehouse_materials_preparred_date: ColumnType<
    Date,
    string | undefined,
    string | undefined
  > | null
  warehouse_materials_given_email: string | null
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
  printer_use: string | null
  is_customer_charged: boolean | null
  customer_charge_invoice_number: string | null
  customer_charge_invoice_date: ColumnType<
    Date,
    string | undefined,
    string | undefined
  > | null
}

// Materials associated with repositories
export interface WorkRepoMaterialTable {
  id: Generated<number>
  work_repo_id: number
  part_number: string | null
  unit_of_measure: string | null
  quantity: number | null
}

// Pieces/components of repositories
export interface WorkRepoPieceTable {
  id: Generated<number>
  work_repo_id: number
  piece: string | null
  size: string | null
  quantity: number | null
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
}

// Status tracking for repositories
export interface WorkRepoStatusTable {
  id: Generated<number>
  sort: number | null
  name: string | null
  notifiy_emails: string | null
  notify_creator: boolean | null
  subject_line: string | null
  work_template_id: number | null
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
}

// Notes/comments for repositories
export interface WorkRepoNoteTable {
  id: Generated<number>
  work_repo_id: number
  user_mail: string | null
  note: string | null
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
}
