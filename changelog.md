# Changelog

## [0.1.9] [2025-05-22]

- Added new endpoint to get unacknowledged POs
- Added new endpoint to process acknowledgments
- Added new table to track POs
- Added new service to handle acknowledgment processing

## [0.1.8] [2025-05-22]

- Added new environment variable `BSN_FILE_PATH_CSV` for absolute path control of CSV file storage
- Updated TypeScript environment declarations
- Modified CSV file processing logic for BSN

## [0.1.7] [2025-05-22]

- Added docs endpoint for documentation
- Vibed out the documentation

## [0.1.6] [2025-05-22]

- Add style variant map table for converting since we cannot use poly yet
- Made the BSN po submit even less strict on certain properties
- Modified BSN endpoint to check for known skus

## [0.1.5] [2025-05-21]

- Added varpro restful api library
- Added gerenal order details endpoint
- Added BSN specific order details endpoint

## [0.1.4] [2025-05-14]

- BSN zod making more things optional
- Create CSV file for BSN working
- Need to change save location

## [0.1.3] [2025-05-08]

- Fixed zod and removed api<PERSON>ey being mandatory in body

## [0.1.2] [2025-05-06]

- Added test endpoints for BSN

## [0.1.1] [2025-05-06]

- added request logging on the api endpoints
- fixed tables creation type
